{{#
    @name Perspectives by category Index
#}}

<!-- /perspective/perspectives_categories/index.antlers.html -->
<div class="content-area-a js-slider-over pt-10 bg-dark">
	{{ partial:layout/header theme="dark" }}
	<section class="c-text-feature z-30 relative snap-start pt-10">
		<div class="container px-4 pb-10 lg:pb-14">
			<h1 class="flex font-bold text-56 text-white">
				<div class="c-label-updated"></div>
				A look at some of<br />our work
			</h1>
			<div class="text-24 font-semibold mt-7 ml-[68px] max-w-[908px]">
				<p class="text-white mb-7">At Platinum Seed, we're focused on creating strategic work that resonates with our clients' customers.</p>
				<p class="text-white">Always seeking long lasting relationships, delivering measurable ROI and mutual growth with like-minded businesses.</p>
			</div>
		</div>
	</section>
</div>


{{ entries as="items" }}
		<div class="content-area-b pt-6 rounded-t-lg -mt-7 z-20 relative bg-white">
			<div class="container px-4">
				<div class="c-tag-filter flex flex-wrap pb-5 mb-9">
					<a href="/our-work/" class="text-dark px-4 py-1 border-b border-grey hover:font-bold hover:border-accent transition-all duration-300 ease-linear inline-block">
						<span class="inline-block" style="width: max-content;">
							<span class="block font-bold opacity-0 h-0 overflow-hidden" aria-hidden="true">All</span>
							<span class="block -mt-[1.25rem]">All</span>
						</span>
					</a>
					{{ taxonomy from="case_study_categories" }}
						{{ if page:slug == slug }}
							<a href="/our-work/" class="text-dark px-4 py-1 border-b font-bold border-accent inline-block">
						{{ else }}
							<a href="/our-work/case-study-categories/{{slug}}/" class="text-dark px-4 py-1 border-b border-grey hover:font-bold hover:border-accent transition-all duration-300 ease-linear inline-block">
						{{ /if }}
							<span class="inline-block" style="width: max-content;">
								<span class="block font-bold opacity-0 h-0 overflow-hidden" aria-hidden="true">{{title}}</span>
								<span class="block -mt-[1.25rem]">{{title}}</span>
							</span>
						</a>
					{{ /taxonomy }}
				</div>
				
				
				<div class="c-tag-filter flex flex-wrap gap-4 pb-5 border-b border-dark mb-9">
					{{ partial:atoms/tag url="/our-work/" label="All Featured" }}
					{{ filtered_tags }}
						{{ if page:slug == slug }}
							<div>{{ partial:atoms/tag url="/our-work/tags/{{slug}}/" :label="title" selected="true" }}</div>
						{{ else }}
							<div>{{ partial:atoms/tag url="/our-work/tags/{{slug}}/" :label="title" }}</div>
						{{ /if }}
					{{ /filtered_tags }}
				</div>
				<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 -mx-7">
					{{ unless no_results }}
						{{ items }}
							{{ partial:components/our-work-card :bg="card_bg" as='a' :card_url="url" cardbg="bg-white" :image="listing_image:url" :title="hero_title" :brand="title" }}
						{{ /items }}
					{{ else }}
						<div class="md:col-span-6">
							{{ trans:strings.no_results }}
						</div>
					{{ /unless }}
				</div>
			</div>
		</div>
{{ /entries }}
{{ partial:layout/footer }}
<!-- End: /perspective/index.antlers.html -->